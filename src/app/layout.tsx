import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import Navbar from "@/components/navigation/Navbar";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "<PERSON> | Frontend-Focused Fullstack Developer",
  description:
    "Building intuitive, performant web experiences that scale. 3+ years of experience in React, Next.js, and modern web technologies.",
  keywords:
    "fullstack developer, frontend developer, React, Next.js, TypeScript, web development",
  authors: [{ name: "<PERSON>" }],
  openGraph: {
    title: "<PERSON> | Frontend-Focused Fullstack Developer",
    description: "Building intuitive, performant web experiences that scale",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <Navbar />
          <main>{children}</main>
        </ThemeProvider>
      </body>
    </html>
  );
}
