'use client'

import { motion } from "motion/react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Linkedin, Mail } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="relative px-6 py-10 bg-gradient-to-r from-gray-100 via-blue-50 to-purple-50 dark:from-black dark:via-blue-950 dark:to-purple-950 text-center">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="mb-6 flex justify-center gap-6">
          <a
            href="https://github.com"
            target="_blank"
            aria-label="GitHub"
            className="inline-block p-3 glass rounded-full hover:scale-110 transition"
          >
            <Github className="w-6 h-6" />
          </a>
          <a
            href="https://linkedin.com"
            target="_blank"
            aria-label="LinkedIn"
            className="inline-block p-3 glass rounded-full hover:scale-110 transition"
          >
            <Linkedin className="w-6 h-6" />
          </a>
          <a
            href="mailto:<EMAIL>"
            aria-label="Email"
            className="inline-block p-3 glass rounded-full hover:scale-110 transition"
          >
            <Mail className="w-6 h-6" />
          </a>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          &copy; {new Date().getFullYear()} Your Name. All rights reserved.
        </p>
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="inline-block p-2 glass rounded-full transition hover:scale-110"
          aria-label="Back to top"
        >
          <ArrowUp className="w-6 h-6 text-blue-600 dark:text-blue-400" />
        </button>
      </motion.div>
    </footer>
  )
}
