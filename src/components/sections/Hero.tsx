"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>edin, Mail } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function Hero() {
  // Animation settings for stagger
  const staggerContainer = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: { staggerChildren: 0.15 },
    },
  };

  const fadeUp = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", duration: 0.75 },
    },
  };

  return (
    <section
      aria-label="Hero Section"
      className="relative min-h-[90vh] flex flex-col justify-between items-center overflow-x-hidden bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-950 dark:via-blue-950 dark:to-purple-950"
    >
      {/* Animated Decorative Background Blobs */}
      <motion.div
        aria-hidden="true"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 0.3, scale: 1 }}
        transition={{ duration: 1.2 }}
        className="absolute top-[-6rem] left-[-8rem] w-[30vw] h-[30vw] rounded-full bg-gradient-to-tr from-blue-500 via-purple-400 to-pink-400 blur-2xl"
      />
      <motion.div
        aria-hidden="true"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 0.25, scale: 1 }}
        transition={{ duration: 1.5 }}
        className="absolute bottom-[-6rem] right-[-8rem] w-[24vw] h-[24vw] rounded-full bg-gradient-to-br from-blue-400 via-purple-400 to-pink-300 blur-2xl"
      />
      {/* Glassmorphic Overlay */}
      <div className="absolute inset-0 glass pointer-events-none z-0" />

      {/* Responsive Hero */}
      <motion.div
        variants={staggerContainer}
        initial="hidden"
        animate="show"
        className="relative z-10 w-full flex flex-col items-center pt-24 pb-16 px-6"
      >
        {/* Profile Image and Ring */}
        <motion.div
          variants={fadeUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <div className="relative flex items-center justify-center">
            <div
              className="absolute w-[128px] h-[128px] rounded-full bg-gradient-to-tr from-blue-400 to-pink-500 opacity-40 blur-lg animate-spin-slow"
              aria-hidden="true"
            />
            <Image
              src="/images/profile.jpg"
              alt="Professional headshot"
              width={120}
              height={120}
              priority
              className="rounded-full border-[5px] border-white dark:border-gray-900 shadow-lg object-cover"
            />
          </div>
        </motion.div>

        {/* Name & Title */}
        <motion.h1
          variants={fadeUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2 }}
          className="mb-3 font-extrabold text-neutral-900 dark:text-white"
          style={{
            fontSize: "clamp(2.2rem, 7vw, 4rem)",
            lineHeight: 1.1,
          }}
        >
          Frontend-Focused Fullstack Developer
        </motion.h1>

        {/* Subtitle/Tagline */}
        <motion.h2
          variants={fadeUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.3 }}
          className="mb-6 text-xl sm:text-2xl text-blue-600 dark:text-blue-400 font-bold"
        >
          Building intuitive, scalable & performant web experiences
        </motion.h2>

        {/* Value Pillars / Stats (Optional) */}
        <motion.ul
          variants={fadeUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.4 }}
          aria-label="Key achievements and statistics"
          className="flex flex-wrap gap-5 mb-8 justify-center"
        >
          <li className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full px-4 py-2 font-semibold text-sm glass">
            3+ Years Experience
          </li>
          <li className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full px-4 py-2 font-semibold text-sm glass">
            100% Accessibility
          </li>
          <li className="bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 rounded-full px-4 py-2 font-semibold text-sm glass">
            99.99% Uptime
          </li>
        </motion.ul>

        {/* CTA Buttons */}
        <motion.div
          variants={fadeUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.5 }}
          className="flex flex-wrap gap-4 justify-center mb-10"
          role="group"
        >
          <Link href="#projects" legacyBehavior>
            <a
              className="relative inline-block px-7 py-3 bg-blue-600 text-white font-semibold rounded-full transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="View Projects"
              tabIndex={0}
            >
              View Projects
              <span className="absolute left-0 bottom-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-pink-400 scale-x-0 group-hover:scale-x-100 transition-transform origin-left" />
            </a>
          </Link>
          <Link href="#contact" legacyBehavior>
            <a
              className="relative inline-block px-7 py-3 border-2 border-blue-600 text-blue-600 dark:text-blue-400 font-semibold rounded-full transition hover:bg-blue-100 dark:hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Contact Me"
              tabIndex={0}
            >
              Contact Me
            </a>
          </Link>
        </motion.div>

        {/* Social Links */}
        <motion.nav
          variants={fadeUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.6 }}
          aria-label="Social Links"
          className="flex gap-5 justify-center mb-8"
        >
          {[
            { icon: Github, href: "https://github.com", label: "GitHub" },
            { icon: Linkedin, href: "https://linkedin.com", label: "LinkedIn" },
            { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
          ].map(({ icon: Icon, href, label }) => (
            <a
              key={label}
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 glass rounded-full hover:scale-110 transition-transform focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label={label}
              tabIndex={0}
            >
              <Icon className="w-6 h-6" />
            </a>
          ))}
        </motion.nav>

        {/* Animated scroll indicator */}
        <motion.div
          aria-hidden="true"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.4, y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 1.5 }}
          className="absolute bottom-6 left-1/2 transform -translate-x-1/2"
        >
          <ArrowDown
            className="w-8 h-8 text-blue-500 dark:text-blue-300"
            aria-label="Scroll down for more"
          />
        </motion.div>
      </motion.div>
    </section>
  );
}
