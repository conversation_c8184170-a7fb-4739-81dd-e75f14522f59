"use client";

import { motion } from "motion/react";
import { CheckCircle } from "lucide-react";

const skillCategories = [
  {
    category: "Frontend",
    items: [
      { name: "React", icon: "/icons/react.svg" },
      { name: "Next.js", icon: "/icons/nextjs.svg" },
      { name: "TailwindCSS", icon: "/icons/tailwind.svg" },
      { name: "Framer Motion", icon: "/icons/framer-motion.svg" },
      // Add more
    ],
  },
  {
    category: "Backend",
    items: [
      { name: "Node.js", icon: "/icons/nodejs.svg" },
      { name: "Express", icon: "/icons/express.svg" },
      { name: "SQL/NoSQL", icon: "/icons/db.svg" },
      // Add more
    ],
  },
  {
    category: "Tools",
    items: [
      { name: "Git", icon: "/icons/git.svg" },
      { name: "Docker", icon: "/icons/docker.svg" },
      { name: "CI/CD", icon: "/icons/cicd.svg" },
      // Add more
    ],
  },
  {
    category: "Others",
    items: [
      { name: "UI/UX Principles", icon: "/icons/uiux.svg" },
      { name: "Agile", icon: "/icons/agile.svg" },
      // Add more
    ],
  },
];

export default function Skills() {
  return (
    <section id="skills" className="py-24 px-6 container mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <h2 className="text-5xl font-bold mb-6">Skills & Tools</h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          A toolkit for building scalable and delightful web experiences
        </p>
      </motion.div>
      <div className="grid md:grid-cols-2 xl:grid-cols-4 gap-12">
        {skillCategories.map((cat) => (
          <motion.div
            key={cat.category}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="glass rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold mb-4">{cat.category}</h3>
            <div className="grid grid-cols-2 gap-5">
              {cat.items.map((skill) => (
                <div
                  key={skill.name}
                  className="flex items-center gap-4 group hover:scale-105 transition-all"
                >
                  <img
                    src={skill.icon}
                    alt={`${skill.name} icon`}
                    className="w-8 h-8"
                  />
                  <span className="font-semibold text-gray-700 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                    {skill.name}
                  </span>
                  <CheckCircle className="ml-auto w-4 h-4 text-blue-400 opacity-0 group-hover:opacity-100 transition" />
                </div>
              ))}
            </div>
          </motion.div>
        ))}
      </div>
    </section>
  );
}
