'use client'

import { motion, useScroll, useTransform } from "motion/react"
import { ExternalLink, Github } from 'lucide-react'
import Image from 'next/image'
import { useRef } from 'react'

const projects = [
  {
    id: 1,
    title: 'E-Commerce Dashboard',
    description: 'React-based admin panel with real-time analytics, optimized for performance with lazy loading and code splitting. Improved load time by 40%.',
    image: '/projects/ecommerce.jpg',
    tech: ['React', 'Next.js', 'TailwindCSS', 'Node.js', 'PostgreSQL'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com',
    metrics: ['40% faster load time', '99.9% uptime', '10K+ daily users'],
  },
  {
    id: 2,
    title: 'Social Media App',
    description: 'Full-stack social platform with real-time messaging, image uploads, and responsive design. Focus on intuitive UX and accessibility.',
    image: '/projects/social.jpg',
    tech: ['Next.js', 'TypeScript', 'Socket.io', 'MongoDB', 'AWS S3'],
    liveUrl: 'https://example.com',
    githubUrl: 'https://github.com',
    metrics: ['Real-time messaging', 'WCAG AA compliant', '5K+ users'],
  },
  // Add 4-6 projects total
]

function ProjectCard({ project, index }: { project: typeof projects[0]; index: number }) {
  const ref = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start'],
  })

  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0.8, 1, 1, 0.8])
  const x = useTransform(
    scrollYProgress,
    [0, 0.3, 0.7, 1],
    [index % 2 === 0 ? -100 : 100, 0, 0, index % 2 === 0 ? 100 : -100]
  )

  return (
    <motion.div
      ref={ref}
      style={{ opacity, scale, x }}
      className="glass rounded-3xl p-8 mb-12"
    >
      <div className="grid md:grid-cols-2 gap-8 items-center">
        <div className={index % 2 === 0 ? 'order-1' : 'order-2'}>
          <Image
            src={project.image}
            alt={project.title}
            width={600}
            height={400}
            className="rounded-2xl shadow-2xl"
            loading="lazy"
          />
        </div>

        <div className={index % 2 === 0 ? 'order-2' : 'order-1'}>
          <h3 className="text-3xl font-bold mb-4">{project.title}</h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {project.description}
          </p>

          <div className="flex flex-wrap gap-2 mb-6">
            {project.tech.map((tech) => (
              <span
                key={tech}
                className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium"
              >
                {tech}
              </span>
            ))}
          </div>

          <div className="grid grid-cols-3 gap-4 mb-6">
            {project.metrics.map((metric) => (
              <div key={metric} className="text-center p-3 glass rounded-lg">
                <p className="text-sm font-semibold">{metric}</p>
              </div>
            ))}
          </div>

          <div className="flex gap-4">
            <a
              href={project.liveUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            >
              <ExternalLink className="w-4 h-4" />
              Live Demo
            </a>
            <a
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-6 py-3 border-2 border-gray-300 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition"
            >
              <Github className="w-4 h-4" />
              Code
            </a>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default function Projects() {
  return (
    <section id="projects" className="py-24 px-6 container mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <h2 className="text-5xl font-bold mb-4">Featured Projects</h2>
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Showcasing scalable web applications with emphasis on frontend excellence and fullstack capabilities
        </p>
      </motion.div>

      {projects.map((project, index) => (
        <ProjectCard key={project.id} project={project} index={index} />
      ))}
    </section>
  )
}
