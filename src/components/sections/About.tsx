'use client'

import { motion } from "motion/react"
import Timeline from "../ui/Timeline"
import Radar from "../ui/Radar"


export default function About() {
  return (
    <section id="about" className="py-24 px-6 container mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.9 }}
        className="max-w-5xl mx-auto text-center mb-16"
      >
        <h2 className="text-5xl font-bold mb-6">About Me</h2>
        <div className="text-lg text-gray-600 dark:text-gray-300 mb-8">
          <motion.p
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="mb-6"
          >
            I'm a frontend-focused fullstack developer with <span className="font-semibold text-blue-600 dark:text-blue-400">3+ years of experience</span> building scalable, performant, and accessible web applications.<br />
            I specialize in <span className="font-semibold">React, Next.js, UI/UX optimization, and modern CSS</span>—with hands-on backend skills in Node.js, Express, and databases.
          </motion.p>
          <motion.p
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="mb-4"
          >
            Passionate about clean minimal design, micro-interactions, and seamless responsiveness. I thrive on building intuitive experiences that delight users and meet business goals.
          </motion.p>
        </div>
      </motion.div>
      
      {/* Timeline Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="mb-24"
      >
        <Timeline />
      </motion.div>
      
      {/* Skills Radar Section */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.6, duration: 0.8 }}
      >
        <Radar />
      </motion.div>
    </section>
  )
}
