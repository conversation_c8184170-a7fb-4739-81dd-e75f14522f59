'use client'

import { motion } from "motion/react"
import { Calendar, MapPin, Award } from 'lucide-react'

interface TimelineItem {
  year: string
  title: string
  company?: string
  location?: string
  description: string
  achievements?: string[]
  type: 'work' | 'education' | 'project' | 'achievement'
}

const timelineData: TimelineItem[] = [
  {
    year: '2025',
    title: 'Senior Frontend Developer',
    company: 'Tech Startup',
    location: 'Remote',
    description: 'Leading frontend architecture and mentoring junior developers',
    achievements: [
      'Improved app performance by 45%',
      'Implemented design system',
      'Led team of 4 developers'
    ],
    type: 'work'
  },
  {
    year: '2023',
    title: 'Fullstack Developer',
    company: 'Digital Agency',
    location: 'Mumbai, India',
    description: 'Built scalable web applications using React, Node.js, and cloud services',
    achievements: [
      '15+ client projects delivered',
      'Reduced deployment time by 60%',
      'Implemented CI/CD pipelines'
    ],
    type: 'work'
  },
  {
    year: '2022',
    title: 'Frontend Developer',
    company: 'Startup Inc.',
    location: 'Bangalore, India',
    description: 'Focused on creating responsive, accessible user interfaces',
    achievements: [
      'Achieved 99% accessibility score',
      'Built component library',
      'Optimized for mobile-first design'
    ],
    type: 'work'
  },
  {
    year: '2021',
    title: 'Computer Science Graduate',
    company: 'University Name',
    location: 'India',
    description: 'Bachelor of Technology in Computer Science',
    achievements: [
      'First Class with Distinction',
      'Led coding club',
      'Published research paper'
    ],
    type: 'education'
  }
]

export default function Timeline() {
  return (
    <div className="relative max-w-4xl mx-auto">
      {/* Timeline line */}
      <div className="absolute left-1/2 transform -translate-x-0.5 h-full w-0.5 bg-gradient-to-b from-blue-500 to-purple-500"></div>
      
      <div className="space-y-12">
        {timelineData.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className={`relative flex items-center ${
              index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
            }`}
          >
            {/* Timeline dot */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full border-4 border-white dark:border-gray-900 z-10">
              <div className="absolute inset-0 bg-blue-500 rounded-full animate-ping opacity-20"></div>
            </div>
            
            {/* Content card */}
            <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
              <motion.div
                whileHover={{ scale: 1.02, y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
                className="glass rounded-2xl p-6 shadow-lg"
              >
                {/* Year badge */}
                <div className="inline-flex items-center px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-semibold mb-3">
                  <Calendar className="w-4 h-4 mr-2" />
                  {item.year}
                </div>
                
                {/* Title and company */}
                <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                {item.company && (
                  <div className="flex items-center text-gray-600 dark:text-gray-400 mb-2">
                    <Award className="w-4 h-4 mr-2" />
                    <span className="font-medium">{item.company}</span>
                  </div>
                )}
                {item.location && (
                  <div className="flex items-center text-gray-500 dark:text-gray-500 mb-3">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span className="text-sm">{item.location}</span>
                  </div>
                )}
                
                {/* Description */}
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {item.description}
                </p>
                
                {/* Achievements */}
                {item.achievements && (
                  <ul className="space-y-1">
                    {item.achievements.map((achievement, i) => (
                      <li key={i} className="flex items-start text-sm text-gray-600 dark:text-gray-400">
                        <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                        {achievement}
                      </li>
                    ))}
                  </ul>
                )}
                
                {/* Type indicator */}
                <div className={`inline-block px-2 py-1 rounded text-xs font-medium mt-3 ${
                  item.type === 'work' 
                    ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                    : item.type === 'education'
                    ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400'
                    : 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400'
                }`}>
                  {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                </div>
              </motion.div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
