'use client'

import { motion } from "motion/react"
import { useEffect, useState } from 'react'

interface SkillData {
  skill: string
  level: number // 1-10 scale
  color: string
}

const skillsData: SkillData[] = [
  { skill: 'React/Next.js', level: 9, color: '#61DAFB' },
  { skill: 'TypeScript', level: 8, color: '#3178C6' },
  { skill: 'UI/UX Design', level: 8, color: '#FF6B6B' },
  { skill: 'Node.js', level: 7, color: '#339933' },
  { skill: 'Database Design', level: 7, color: '#336791' },
  { skill: 'DevOps/CI-CD', level: 6, color: '#FFA500' }
]

export default function Radar() {
  const [isVisible, setIsVisible] = useState(false)
  
  useEffect(() => {
    setIsVisible(true)
  }, [])

  const centerX = 200
  const centerY = 200
  const maxRadius = 150
  const numLevels = 5

  // Calculate points for each skill on the radar
  const calculatePoint = (skillIndex: number, level: number) => {
    const angle = (skillIndex * 2 * Math.PI) / skillsData.length - Math.PI / 2
    const radius = (level / 10) * maxRadius
    return {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle)
    }
  }

  // Generate concentric circles
  const circles = Array.from({ length: numLevels }, (_, i) => {
    const radius = ((i + 1) / numLevels) * maxRadius
    return (
      <circle
        key={i}
        cx={centerX}
        cy={centerY}
        r={radius}
        fill="none"
        stroke="currentColor"
        strokeWidth="1"
        opacity="0.2"
      />
    )
  })

  // Generate axis lines
  const axes = skillsData.map((_, index) => {
    const angle = (index * 2 * Math.PI) / skillsData.length - Math.PI / 2
    const endX = centerX + maxRadius * Math.cos(angle)
    const endY = centerY + maxRadius * Math.sin(angle)
    
    return (
      <line
        key={index}
        x1={centerX}
        y1={centerY}
        x2={endX}
        y2={endY}
        stroke="currentColor"
        strokeWidth="1"
        opacity="0.3"
      />
    )
  })

  // Generate the skill polygon path
  const skillPoints = skillsData.map((skill, index) => 
    calculatePoint(index, skill.level)
  )
  
  const pathData = skillPoints
    .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`)
    .join(' ') + ' Z'

  return (
    <div className="max-w-2xl mx-auto">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        whileInView={{ opacity: 1, scale: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-8"
      >
        <h3 className="text-2xl font-bold mb-2">Skills Radar</h3>
        <p className="text-gray-600 dark:text-gray-400">
          Technical proficiency across key areas
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, rotateY: 20 }}
        whileInView={{ opacity: 1, rotateY: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 1, delay: 0.2 }}
        className="glass rounded-3xl p-8"
        style={{ 
          transform: 'perspective(1000px) rotateX(5deg)',
          transformStyle: 'preserve-3d'
        }}
      >
        <svg
          width="400"
          height="400"
          viewBox="0 0 400 400"
          className="mx-auto text-gray-600 dark:text-gray-400"
        >
          {/* Background circles */}
          {circles}
          
          {/* Axis lines */}
          {axes}
          
          {/* Skill polygon */}
          <motion.path
            d={pathData}
            fill="rgba(59, 130, 246, 0.1)"
            stroke="rgba(59, 130, 246, 0.6)"
            strokeWidth="2"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={isVisible ? { pathLength: 1, opacity: 1 } : {}}
            transition={{ duration: 2, delay: 0.5 }}
          />
          
          {/* Skill points */}
          {skillsData.map((skill, index) => {
            const point = calculatePoint(index, skill.level)
            return (
              <motion.g key={skill.skill}>
                <motion.circle
                  cx={point.x}
                  cy={point.y}
                  r="6"
                  fill={skill.color}
                  stroke="white"
                  strokeWidth="2"
                  initial={{ scale: 0, opacity: 0 }}
                  animate={isVisible ? { scale: 1, opacity: 1 } : {}}
                  transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                  whileHover={{ scale: 1.5 }}
                  className="cursor-pointer"
                />
                
                {/* Skill labels */}
                <motion.text
                  x={point.x + (point.x > centerX ? 15 : -15)}
                  y={point.y + 5}
                  textAnchor={point.x > centerX ? 'start' : 'end'}
                  className="text-sm font-medium fill-current"
                  initial={{ opacity: 0 }}
                  animate={isVisible ? { opacity: 1 } : {}}
                  transition={{ duration: 0.5, delay: 1 + index * 0.1 }}
                >
                  {skill.skill}
                </motion.text>
                
                {/* Level indicator */}
                <motion.text
                  x={point.x + (point.x > centerX ? 15 : -15)}
                  y={point.y + 20}
                  textAnchor={point.x > centerX ? 'start' : 'end'}
                  className="text-xs fill-gray-500 dark:fill-gray-400"
                  initial={{ opacity: 0 }}
                  animate={isVisible ? { opacity: 1 } : {}}
                  transition={{ duration: 0.5, delay: 1.2 + index * 0.1 }}
                >
                  {skill.level}/10
                </motion.text>
              </motion.g>
            )
          })}
          
          {/* Center point */}
          <circle
            cx={centerX}
            cy={centerY}
            r="3"
            fill="currentColor"
            opacity="0.5"
          />
        </svg>
        
        {/* Legend */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 1.5 }}
          className="mt-8 grid grid-cols-2 gap-4"
        >
          {skillsData.map((skill, index) => (
            <motion.div
              key={skill.skill}
              initial={{ opacity: 0, x: -20 }}
              animate={isVisible ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.4, delay: 1.6 + index * 0.1 }}
              className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: skill.color }}
              />
              <span className="text-sm font-medium">{skill.skill}</span>
              <span className="ml-auto text-xs text-gray-500 dark:text-gray-400">
                {skill.level}/10
              </span>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  )
}
