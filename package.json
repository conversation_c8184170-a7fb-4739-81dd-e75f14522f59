{"name": "portfolio-app-v1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "motion": "^12.23.22", "next": "15.5.4", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.64.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.11"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.4.0", "typescript": "^5"}}